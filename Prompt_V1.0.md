用Python语言开发一个对彩票（含SSQ与DLT）历史数据进行回归测试的程序LotteryTest V1.0。需求Prompt如下：

一、术语定义：
1.1 期号 
每期的编号（NO列），由1个4位数或5位数组成，期号越小，表示时间越早。在4位数中，第1位代表年份，后3位数字表示序号。比如在SSQ或DLT中，7001，7代表2007年，001表示当年第1期。在5位数中，前两位数字代表年份，后3位数字表示序号，比如在SSQ或DLT中，期号14100，14表示2014年,100表示当年第100期。每年的期号都会从序号001开始。
1）最新1期号码：在指定的数据库中，期号最大的1期的红蓝球号码即为最新1期号码。
2）期号年度跨越：原始数据库中每年的最后1期与下一年的第1期的期号不是连续的，比如，在SSQ原始数据库中，2023年的最后1期是23151期,23151期之后的1期是24001期（2024年的第1期）。在DLT原始数据库中，2023年的最后1期是23150期,23150期之后的1期是24001期（2024年的第1期）。

1.2 红蓝球号码 
1.2.1 每期红蓝球号码 
在原始数据库或其他由原始数据库转化而来的数据库中，每期红蓝球号码一般由7个号码组成。
1）在SSQ的7个球中，前6个球为红球，号码范围为1~33。第7个球为蓝球，号码范围为1~16。
2）在SSQ的7个球中，前5个球为红球，号码范围为1~35。第6与第7个球为蓝球，号码范围为1~12。
1.2.2 单注红蓝球号码
一般由7个号码组成。
1.2.3 多注红蓝球号码
一般由多个单注红蓝球号码组成，其中每注红蓝球号码由7个号码组成。
1.2.4 复式红蓝球号码
一般由多个红球号码与蓝球号码组成，号码的个数一般多于7个。

1.3 大球号码
1）在SSQ中，约定红球号码数值大于16的，蓝球号码数值大于8的均称为大球号码。比如红球号码16是小球号码，红球号码17是大球号码，蓝球号码8是小球号码，蓝球号码9是大球号码。
2）在DLT中，约定红球号码数值大于17的，蓝球号码数值大于6的均称为大球号码。比如红球号码17是小球号码，红球号码18是大球号码，蓝球号码6是小球号码，蓝球号码7是大球号码。

1.4 奇球号码
用号码数值除以2，不能整除的号码称为奇球号码。比如红球号码1、篮球号码3等，都是奇球号码。

1.5 质球号码
号码数值为质数（包括1在内）的号码称为质球号码。比如红球号码1、蓝球号码3等，都是质球号码。

1.6 和值
号码相加之和。

1.7 红球号码AC值
AC值也可称为号码数字复杂度，是指在一注号码组合（只包括单注红蓝球号码，不涉及复式红蓝球号码）中，任意两个红球号码之间不相同的正差值的总个数再减去“正选号码数量-1”的值。在SSQ与DLT中，因为红球个数不同，所以计算方法略有区别。比如，在SSQ中，每注红蓝球号码有6个红球号码，像“6、10、17、19、25、31“这6个红球号码，”任意两个不同号码之间不相同的正差值总个数“有14个（分别是4、7、2、6、11、9、8、12、13、15、14、19、21、25），然后减去”正选号码数量-1“，即5（等于6-1）个，所以红球号码AC值为9。在DLT中，每注红蓝球号码有5个红球号码，像“6、10、17、19、25“这5个红球号码，”任意两个不同号码之间不相同的正差值总个数“有10个（分别是4、7、2、6、11、9、8、13、15、19），然后减去”正选号码数量-1“，即4（等于5-1）个，所以红球号码AC值为6。

1.8 遗漏
红蓝球号码自上期开出到本期间隔的期数。比如：在SSQ中，25002期的红蓝球号码为:9 12 13 15 22 26 +11，其中：红球号码9在上次开出的期号是24149，所以遗漏值为3（在SSQ原始数据库中，25002期在24149期之后的第4期），红球号码12在上次开出的期号是24130，所以遗漏值为22，红球号码13在上次开出的期号是24150，所以遗漏值为2，红球号码15在上次开出的期号是24148，所以遗漏值为4，红球号码22在上次开出的期号是25001，所以遗漏值为0，红球号码26在上次开出的期号是24150，所以遗漏值为2，蓝球号码11在上次开出的期号是24148，所以遗漏值为4。在DLT中，25097期的红蓝球号码为:5 24 25 32 34 +1 9，其中：红球号码5在上次开出的期号是25087，所以遗漏值为9（在DLT原始数据库中，25097期在25087期之后的第10期），红球号码24在上次开出的期号是25096，所以遗漏值为0，红球号码25在上次开出的期号是25092，所以遗漏值为4，红球号码32在上次开出的期号是25092，所以遗漏值为4，红球号码34在上次开出的期号是25089，所以遗漏值为7，蓝球号码1在上次开出的期号是25090，所以遗漏值为6，蓝球号码9在上次开出的期号是25096，所以遗漏值为0。

二、核心算法：
2.1 统计历史出现概率
基于给定的数据库
1）红球号码：统计每个红球号码的历史出现次数并转化为概率（分母为所有红球号码的历史出现次数之和）。然后生成1个2列的红球号码历史出现概率表格，其中第1列为红球号码（按照号码从小到大排序），第2列为红球号码对应的历史出现概率。 
2） 蓝球号码：统计每个蓝球号码的历史出现次数并转化为概率（分母为所有蓝球号码的历史出现次数之和）。然后生成1个2列的蓝球号码历史出现概率表格，其中第1列为蓝球号码（按照号码从小到大排序），第2列为蓝球号码对应的历史出现概率。 

2.2 统计马尔科夫概率
基于给定的数据库，在统计马尔科夫概率前，需先统计出红蓝球号码的历史跟随性概率。
2.2.1 统计红球号码历史跟随性概率
1）在SSQ中，红球号码（特性维度）有33个，则需要先建立1个33行33列的空矩阵，然后统计相关数据库中每相邻两期，前1期出现红球1时，后1期出现红球1至红球33各自出现的次数，然后转化为概率（分母为前1期出现红球1时，后1期出现红球1至红球33出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第1列对应的位置。依次类推...，前1期出现红球33时，后1期出现红球1至红球33各自出现的次数，然后转化为概率（分母为前1期出现红球33时，后1期出现红球1至红球33出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第33列对应的位置。
2）在DLT中，红球号码（特性维度）有35个，则需要先建立1个35行35列的空矩阵，然后统计相关数据库中每相邻两期，前1期出现红球1时，后1期出现红球1至红球35各自出现的次数，然后转化为概率（分母为前1期出现红球1时，后1期出现红球1至红球35出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第1列对应的位置。依次类推...，前1期出现红球35时，后1期出现红球1至红球35各自出现的次数，然后转化为概率（分母为前1期出现红球35时，后1期出现红球1至红球35出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第35列对应的位置。
2.2.2 统计蓝球号码历史跟随性概率
1）在SSQ中，蓝球号码（特性维度）有16个，则需要先建立1个16行16列的空矩阵，然后统计相关数据库中每相邻两期，前1期出现蓝球1时，后1期出现蓝球1至蓝球16各自出现的次数，然后转化为概率（分母为前1期出现蓝球1时，后1期出现蓝球1至蓝球16出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第1列对应的位置。依次类推...，前1期出现蓝球16时，后1期出现蓝球1至蓝球16各自出现的次数，然后转化为概率（分母为前1期出现蓝球16时，后1期出现蓝球1至蓝球16出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第16列对应的位置。
2）在DLT中，蓝球号码（特性维度）有12个，则需要先建立1个12行12列的空矩阵，然后统计相关数据库中每相邻两期，前1期出现蓝球1时，后1期出现蓝球1至蓝球12各自出现的次数，然后转化为概率（分母为前1期出现蓝球1时，后1期出现蓝球1至蓝球12出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第1列对应的位置。依次类推...，前1期出现蓝球12时，后1期出现蓝球1至蓝球12各自出现的次数，然后转化为概率（分母为前1期出现蓝球12时，后1期出现蓝球1至蓝球12出现的总次数，如果总次数为0，则概率都为0）依次存放在空矩阵中第12列对应的位置。
2.2.3 统计红蓝球号码马尔科夫概率
1）在SSQ中，将同一数据库最新1期红蓝球号码中的第1个号码（红球号码）赋值给s1，依次类推，第6个号码（红球号码）赋值给s6，第7个号码（蓝球号码）赋值给s7。 然后将基于同一数据库构建的红球号码历史跟随性概率矩阵（33行33列）中，第s1、s2、s3、s4、s5与s6列的列向量按从左到右的顺序拼接成1个33行6列的新矩阵SA。然后将基于同一数据库构建的红球号码历史出现概率矩阵（33行2列）中，第2列中第s1、s2、s3、s4、s5与s6行的值按从上到下的顺序拼接成1个6行1列的新向量SB。最后将新矩阵SA乘以新向量SB，得到1个SSQ红球号码马尔科夫概率的链列向量（33行1列）。然后生成1个2列的红球号码马尔科夫概率表格，其中第1列为红球号码（按照号码从小到大排序），第2列为红球号码对应的马尔科夫概率。 然后将基于同一数据库构建的蓝球号码历史跟随性概率矩阵（16行16列）中，第s7列的列向量(16行1列)赋值给SSQ蓝球号码马尔科夫概率的链列向量(16行1列)。然后生成1个2列的蓝球号码马尔科夫概率表格，其中第1列为蓝球号码（按照号码从小到大排序），第2列为蓝球号码对应的马尔科夫概率。
2）在DLT中，将同一数据库最新1期红蓝球号码中的第1个号码（红球号码）赋值给s1，依次类推，第5个号码（红球号码）赋值给s5，第6个号码（蓝球号码）赋值给s6，第7个号码（蓝球号码）赋值给s7。 然后将基于同一数据库构建的红球号码历史跟随性概率矩阵（35行35列）中，第s1、s2、s3、s4与s5列的列向量按从左到右的顺序拼接成1个35行5列的新矩阵DA。然后将基于同一数据库构建的红球号码历史出现概率矩阵（35行2列）中，第2列中第s1、s2、s3、s4与s5行的值按从上到下的顺序拼接成1个5行1列的新向量DB。最后将新矩阵DA乘以新向量DB，得到1个DLT红球号码马尔科夫概率的链列向量（35行1列）。然后生成1个2列的红球号码马尔科夫概率表格，其中第1列为红球号码（按照号码从小到大排序），第2列为红球号码对应的马尔科夫概率。 然后将基于同一数据库构建的蓝球号码历史跟随性概率矩阵（12行12列）中，第s6与s7列的列向量按从左到右的顺序拼接成1个12行2列的新矩阵DC。然后将基于同一数据库构建的蓝球号码历史出现概率矩阵（12行2列）中，第2列中第s6与s7行的值按从上到下的顺序拼接成1个2行1列的新向量DD。最后将新矩阵DC乘以新向量DD，得到1个DLT蓝球号码马尔科夫概率的链列向量（12行1列）。然后生成1个2列的蓝球号码马尔科夫概率表格，其中第1列为蓝球号码（按照号码从小到大排序），第2列为蓝球号码对应的马尔科夫概率。

2.3 概率排序
对于任意给定的概率表格（一般为2列，第1列为号码列，第2列为号码对应的概率，比如历史出现概率或者马尔科夫概率值列），可以按如下方法进行概率排序。将第2列按照概率值从大到小的顺序自上而下排列，如果概率值相等，则概率值相等的号码再按号码数值从小到大的顺序自上而下排列，第1列的号码跟随对应的概率值进行同步排序调整。
2.3.1 计算红蓝球号码号码的历史出现概率位置序号
计算某1注或某1期的7个红蓝球号码的历史出现概率的位置序号时，需先指定1个数据库，然后基于给定的数据库，分别统计出红球号码历史出现概率表格与蓝球号码历史出现概率表格。然后将这两个表格按照“概率排序”的方法进行排序调整，分别得到其中第1列红球号码与蓝球号码的历史出现概率的排序关系。最后根据上述第1列红球号码与蓝球号码的历史出现概率的排序关系，来计算出该注或该期红蓝球号码的7个号码的历史出现概率位置序号（位置序号一般从1开始，即第1位的位置序号为1）。
2.3.2 计算红蓝球号码号码的马尔科夫概率位置序号
计算某1注或某1期的7个红蓝球号码的马尔科夫概率的位置序号时，需先指定1个数据库，然后基于给定的数据库，分别统计出红球号码马尔科夫概率表格与蓝球号码马尔科夫概率表格。然后将这两个表格按照“概率排序”的方法进行排序调整，分别得到其中第1列红球号码与蓝球号码的马尔科夫概率的排序关系。最后根据上述第1列红球号码与蓝球号码的马尔科夫概率的排序关系，来计算出该注或该期红蓝球号码的7个号码的马尔科夫概率位置序号（位置序号一般从1开始，即第1位的位置序号为1）。

2.4 筛选号码
2.4.1 第1组复式红蓝球号码
1）基于给定的数据库，将其中的红球号码与蓝球号码分别去重复后，组成第1组复式红蓝球号码。
2.4.2 第2组复式红蓝球号码：
1）基于给定的数据库，分别统计出红蓝球号码的历史出现概率。
2）对红球号码历史出现概率表格与蓝球号码历史出现概率表格，分别按“概率排序”算法进行排序，选出其中红球历史出现概率最大的前NR个红球号码与蓝球历史出现概率最大的前NB个蓝球号码，组成第2组复式红蓝球号码。
2.4.3 第3组复式红蓝球号码：
1）基于给定的数据库，分别统计出红蓝球号码马尔科夫概率表格。
2）对红球号码马尔科夫概率表格与蓝球号码马尔科夫概率表格，分别按“概率排序”算法进行排序，选出其中红球马尔科夫概率最大的前NR个红球号码与蓝球马尔科夫概率最大的前NB个蓝球号码，组成第3组复式红蓝球号码。

2.5 遗漏值计算
2.5.1 基于给定的数据库来计算数据库中的红蓝球号码的遗漏值
基于给定的数据库，在计算某1期的7个红蓝球号码的遗漏值时，如果该期红蓝球号码已经被包含在给定的数据库之内，则直接按照“1.8 遗漏”来计算该期的7个红蓝球号码的遗漏值。
2.5.2 基于给定的数据库来计算数据库之外的红蓝球号码的遗漏值
基于给定的数据库，在计算某1注或某1期的7个红蓝球号码的遗漏值时，如果该注或该期的红蓝球号码不在给定的数据库之内，则需要先将该注或该期的7个红蓝球号码拼接在给定的数据库中7列红蓝球号码的下方（即该注或该期的7个红蓝球号码中的第1个号码，拼接在给定的数据库中7列红蓝球号码的第1列号码的下方，该注或该期的7个红蓝球号码中的第2个号码，拼接在给定的数据库中7列红蓝球号码的第2列号码的下方，以此类推）。然后再计算在拼接后形成的新的数据库中，该注或该期的7个红蓝球号码自上次出现到本次出现，中间间隔的注数或期数，即为该注或该期的7个红蓝球号码对应的遗漏值。

2.6 比对答案
用筛选后的若干组复式红蓝球号码或者经过特性筛选后的若干组多注红蓝球号码在与答案数据库比对时，需要将筛选的每一组复式红蓝球号码或经过特性筛选后每一组多注中的每注红蓝球号码分别与答案数据中的每期红蓝球号码（7个号码）进行一一比对，找出红球号码相等与蓝球号码相等的数量之和最大的数字作为最大命中情况。最大命中情况下的答案数据（红蓝球号码）对应的期号即为中奖期号，如果有多个中奖期号，则以期号最小的1期作为中奖期号。

三、功能定义：
3.1 用户交互
3.1.1 选择数据
询问用户想选择哪种彩票数据：1）SSQ、2）DLT。然后根据用户选择，仅读取相关数据。
1）若用户选择SSQ，读取指定excel文件（“D:\Lottery”路径下的"lottery_data_all.xlsx"文件）中，"SSQ_data_all"标签页下A列至O列所有数据，将其中的A列、I列至O列数据（共计8列数据）保存为合适类型的变量，变量名为ssqhistory_all，作为原始数据库。其中A列为期号列，I列至N列均为红球列，O列为蓝球列。读取完数据后，按第一列（期号列）序号从小到大往下排列。
2）若用户选择DLT，读取指定excel文件（“D:\Lottery”路径下的"lottery_data_all.xlsx"文件）中，"DLT_data_all"标签页下A列至N列所有数据，将其中的A列、H列至N列数据（共计8列数据）保存为合适类型的变量，变量名为dlthistory_all，作为原始数据库。其中A列为期号列，H列至L列均为红球列，M列与N列均为蓝球列。读取完数据后，按第一列（期号列）序号从小到大往下排列。
3.1.2 选择功能
询问用户想选择哪个功能：1）统计特性、2）回归测试、3）批量测试。然后根据用户选择，进入对应功能。

3.2 统计特性
基于读取的原始数据库，按下述要求来统计每期红蓝球号码的特性，然后将结果保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中，命名规则为：统计特性_AAAA_BBBB_CCCC，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用原始数据库中的期号总数来替代、CCCC用实际日期来替代。
3.2.1 在SSQ中
1）统计红蓝球号码的遗漏特性：即从5001期开始（仅统计遗漏特性从5001期开始，其余特性均全部统计），按照“2.5.1 基于给定的数据库来计算数据库中的红蓝球号码的遗漏值”方法，计算每期的7个红蓝球号码的遗漏值。然后在Excel文件中的“红蓝球号码的遗漏特性”标签页下，分行打印每期期号（对应的，从5001期开始）、红蓝球号码（每个号码1列）、红蓝球号码对应的遗漏值（每个遗漏值1列）。
2）统计红球号码的大小特性：即统计每期的6个红球号码中大球号码个数。然后生成1个2列的红球大球号码个数分布的表格，第1列为红球大球号码个数情况，第2列为红球大球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码大小特性”标签页下。
3）统计蓝球号码的大小特性：即统计每期的1个蓝球号码中大球号码个数。然后生成1个2列的蓝球大球号码个数分布的表格，第1列为蓝球大球号码个数情况，第2列为蓝球大球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码大小特性”标签页下。
4）统计红球号码的奇偶特性：即统计每期的6个红球号码中奇球号码个数。然后生成1个2列的红球奇球号码个数分布的表格，第1列为红球奇球号码个数情况，第2列为红球奇球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码奇偶特性”标签页下。
5）统计蓝球号码的奇偶特性：即统计每期的1个蓝球号码中奇球号码个数。然后生成1个2列的蓝球奇球号码个数分布的表格，第1列为蓝球奇球号码个数情况，第2列为蓝球奇球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码奇偶特性”标签页下。
6）统计红球号码的质合特性：即统计每期的6个红球号码中质球号码个数。然后生成1个2列的红球质球号码个数分布的表格，第1列为红球质球号码个数情况，第2列为红球质球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码质合特性”标签页下。
7）统计蓝球号码的质合特性：即统计每期的1个蓝球号码中质球号码个数。然后生成1个2列的蓝球质球号码个数分布的表格，第1列为蓝球质球号码个数情况，第2列为蓝球质球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码质合特性”标签页下。
8）统计红球号码的和值特性，即计算每期的6个红球号码的和值。然后生成1个2列的红球号码和值分布的表格，第1列为红球号码的和值范围，按1到20、21到40，每20一个区间，以此类推，第2列为红球号码和值处于对应的和值范围之中的出现次数。然后保存在Excel文件中的“红球号码和值特性”标签页下。
9）统计红球号码的AC值特性，即计算每期的6个红球号码的AC值。然后生成1个2列的红球号码AC值分布的表格，第1列为红球号码AC值情况，第2列为红球号码AC值对应的出现次数。然后保存在Excel文件中的“红球号码AC值特性”标签页下。
3.2.2 在DLT中
1）统计红蓝球号码的遗漏特性：即从9001期开始（仅统计遗漏特性从9001期开始，其余特性均全部统计），按照“2.5.1 基于给定的数据库来计算数据库中的红蓝球号码的遗漏值”方法，计算每期的7个红蓝球号码的遗漏值。然后在Excel文件中的“红蓝球号码的遗漏特性”标签页下，分行打印每期期号（对应的，从9001期开始）、红蓝球号码（每个号码1列）、红蓝球号码对应的遗漏值（每个遗漏值1列）。
2）统计红球号码的大小特性：即统计每期的5个红球号码中大球号码个数。然后生成1个2列的红球大球号码个数分布的表格，第1列为红球大球号码个数情况，第2列为红球大球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码大小特性”标签页下。
3）统计蓝球号码的大小特性：即统计每期的2个蓝球号码中大球号码个数。然后生成1个2列的蓝球大球号码个数分布的表格，第1列为蓝球大球号码个数情况，第2列为蓝球大球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码大小特性”标签页下。
4）统计红球号码的奇偶特性：即统计每期的5个红球号码中奇球号码个数。然后生成1个2列的红球奇球号码个数分布的表格，第1列为红球奇球号码个数情况，第2列为红球奇球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码奇偶特性”标签页下。
5）统计蓝球号码的奇偶特性：即统计每期的2个蓝球号码中奇球号码个数。然后生成1个2列的蓝球奇球号码个数分布的表格，第1列为蓝球奇球号码个数情况，第2列为蓝球奇球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码奇偶特性”标签页下。
6）统计红球号码的质合特性：即统计每期的5个红球号码中质球号码个数。然后生成1个2列的红球质球号码个数分布的表格，第1列为红球质球号码个数情况，第2列为红球质球号码个数对应的出现次数。然后保存在Excel文件中的“红球号码质合特性”标签页下。
7）统计蓝球号码的质合特性：即统计每期的2个蓝球号码中质球号码个数。然后生成1个2列的蓝球质球号码个数分布的表格，第1列为蓝球质球号码个数情况，第2列为蓝球质球号码个数对应的出现次数。然后保存在Excel文件中的“蓝球号码质合特性”标签页下。
8）统计红球号码的和值特性，即计算每期的5个红球号码的和值。然后生成1个2列的红球号码和值分布的表格，第1列为红球号码的和值范围，按1到20、21到40，每20一个区间，以此类推，第2列为红球号码和值处于对应的和值范围之中的出现次数。然后保存在Excel文件中的“红球号码和值特性”标签页下。
9）统计蓝球号码的和值特性，即计算每期的2个蓝球号码的和值。然后生成1个2列的蓝球号码和值分布的表格，第1列为蓝球号码的和值范围，按1到5、6到10，每5一个区间，以此类推，第2列为蓝球号码和值处于对应的和值范围之中的出现次数。然后保存在Excel文件中的“蓝球号码和值特性”标签页下。
10）统计红球号码的AC值特性，即计算每期的5个红球号码的AC值。然后生成1个2列的红球号码AC值分布的表格，第1列为红球号码AC值情况，第2列为红球号码AC值对应的出现次数。然后保存在Excel文件中的“红球号码AC值特性”标签页下。

3.3 回归测试
询问用户想选择哪一组复式红蓝球号码进行回归测试：1）第1组复式红蓝球号码、2）第2组复式红蓝球号码、3）第3组复式红蓝球号码。然后根据用户选择，进入对应子功能。
3.3.1 对第1组复式红蓝球号码进行回归测试
3.3.1.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
3）定义当前数据库的数据范围：询问用户，根据用户输入来定义当前数据库所需的数据范围。如果用户输入一个非零正整数，比如6，则将原始数据库中，起始期号之前的6期数据（不包括起始期号对应的红蓝球号码），总共6期的红蓝球号码作为当前数据库。
4）定义当前计算遗漏值的数据库：将原始数据库中，起始期号之前的所有数据（不包括起始期号对应的红蓝球号码），作为当前计算遗漏值的数据库。
3.3.1.2 循环主体
1）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第1组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于当前计算遗漏值的数据库时的遗漏值（按照“2.5.2 基于给定的数据库来计算数据库之外的红蓝球号码的遗漏值”方法）。
2）循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号和用户定义的当前数据库和答案数据库的数据范围（两个数据范围值都不需要用户重复输入），来更新当前数据库、当前答案数据库与当前计算遗漏值的数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第1组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于更新后的当前计算遗漏值的数据库时的遗漏值（按照“2.5.2 基于给定的数据库来计算数据库之外的红蓝球号码的遗漏值”方法）。重复“循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
3.3.1.3 信息打印
程序在进行“回归测试”时，需要在一开始打印显示：需要回归测试的总期数（根据用户指定的起始期号计算出需要测试的总期数），然后每当完成100期回归测试时，打印显示当前已完成多少期的测试、当前数据库包含多少期数据（如果起始期号及之前的红蓝球数据不满足当前数据库范围的要求，则需要显示真实的期数）、当前起始期号，及当前起始期号对应的红蓝球号码等这些信息。在完成所有的回归测试循环之后，再最终打印显示本轮回归测试的最大命中情况的分布情况。
3.3.1.4 保存结果
将本轮回归测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中。Excel文件的命名规则为：回归测试1_AAAA_BBBB_CCCC_DDDD_EEEE，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用当前数据库的数据范围值来替代、DDDD用当前答案数据库的数据范围值来替代、EEEE用最大命中情况等于7的总次数来替代。保存的内容要求为：
1）期号：即起始期号（1列）
2）第1组复式红蓝球号码（红球号码与蓝球号码各1列，号码之间用逗号隔开，共2列）
3）第1组复式红蓝球号码中红蓝球号码的个数（红球号码的个数与蓝球号码的个数各1列，共2列）
4）最大命中情况（1列）
5）最大命中情况出现的次数（1列）
6）中奖期号（1列）
7）中奖期号对应的红蓝球号码（每个号码各1列，共7列）
8）中奖期号对应的红蓝球号码的遗漏值：（每个遗漏值各1列，共7列）。 
3.3.2 对第2组复式红蓝球号码进行回归测试
3.3.2.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
3）定义红蓝球号码的个数要求：询问用户，根据用户输入来定义第2组复式红蓝球号码所需的红蓝球号码的个数。用户需输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给NR，第2个数字传递给NB。比如：用户输入22,6，则NR等于22，NB等于6。
4）定义当前数据库的数据范围：询问用户，根据用户输入来定义当前数据库所需的数据范围。如果用户输入一个非零正整数，比如15，则将原始数据库中，起始期号之前的15期数据（不包括起始期号对应的红蓝球号码），总共15期的红蓝球号码作为当前数据库。
3.3.2.2 循环主体
1）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第2组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于当前数据库时的历史出现概率位置序号（按照“2.3.1 计算红蓝球号码号码的历史出现概率位置序号”方法）。
2）循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号和用户定义的当前数据库和答案数据库的数据范围（两个数据范围值都不需要用户重复输入），来更新当前数据库与当前答案数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第2组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于更新后的当前数据库时的历史出现概率位置序号（按照“2.3.1 计算红蓝球号码号码的历史出现概率位置序号”方法）。重复“循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
3.3.2.3 信息打印
程序在进行“回归测试”时，需要在一开始打印显示：需要回归测试的总期数（根据用户指定的起始期号计算出需要测试的总期数），然后每当完成100期回归测试时，打印显示当前已完成多少期的测试、当前数据库包含多少期数据（如果起始期号及之前的红蓝球数据不满足当前数据库范围的要求，则需要显示真实的期数）、当前起始期号，及当前起始期号对应的红蓝球号码等这些信息。在完成所有的回归测试循环之后，再最终打印显示本轮回归测试的最大命中情况的分布情况。
3.3.2.4 保存结果
将本轮回归测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中。Excel文件的命名规则为：回归测试2_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用当前数据库的数据范围值来替代、DDDD用NR值来替代、EEEE用NB值来替代、FFFF用当前答案数据库的数据范围值来替代、GGGG用最大命中情况等于7的总次数来替代。保存的内容要求为：
1）期号：即起始期号（1列）
2）第2组复式红蓝球号码（红球号码与蓝球号码各1列，号码之间用逗号隔开，共2列）
3）第2组复式红蓝球号码中红蓝球号码的个数（红球号码的个数与蓝球号码的个数各1列，共2列）
4）最大命中情况（1列）
5）最大命中情况出现的次数（1列）
6）中奖期号（1列）
7）中奖期号对应的红蓝球号码（每个号码各1列，共7列）
8）中奖期号对应的红蓝球号码的历史出现概率位置序号（每个位置序号各1列，共7列）。 
3.3.3 对第3组复式红蓝球号码进行回归测试
3.3.3.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
3）定义红蓝球号码的个数要求：询问用户，根据用户输入来定义第2组复式红蓝球号码所需的红蓝球号码的个数。用户需输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给NR，第2个数字传递给NB。比如：用户输入22,6，则NR等于22，NB等于6。
4）定义当前数据库的数据范围：询问用户，根据用户输入来定义当前数据库所需的数据范围。如果用户输入一个非零正整数，比如15，则将原始数据库中，起始期号之前的15期数据（不包括起始期号对应的红蓝球号码），总共15期的红蓝球号码作为当前数据库。
3.3.3.2 循环主体
1）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第3组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于当前数据库时的马尔科夫概率位置序号（按照“2.3.2 计算红蓝球号码号码的马尔科夫概率位置序号”方法）。
2）循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号和用户定义的当前数据库和答案数据库的数据范围（两个数据范围值都不需要用户重复输入），来更新当前数据库与当前答案数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第3组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况和计算中奖期号对应的7个红蓝球号码在基于更新后的当前数据库时的马尔科夫概率位置序号（按照“2.3.2 计算红蓝球号码号码的马尔科夫概率位置序号”方法）。重复“循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
3.3.3.3 信息打印
程序在进行“回归测试”时，需要在一开始打印显示：需要回归测试的总期数（根据用户指定的起始期号计算出需要测试的总期数），然后每当完成100期回归测试时，打印显示当前已完成多少期的测试、当前数据库包含多少期数据（如果起始期号及之前的红蓝球数据不满足当前数据库范围的要求，则需要显示真实的期数）、当前起始期号，及当前起始期号对应的红蓝球号码等这些信息。在完成所有的回归测试循环之后，再最终打印显示本轮回归测试的最大命中情况的分布情况。
3.3.3.4 保存结果
将本轮回归测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中。Excel文件的命名规则为：回归测试3_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用当前数据库的数据范围值来替代、DDDD用NR值来替代、EEEE用NB值来替代、FFFF用当前答案数据库的数据范围值来替代、GGGG用最大命中情况等于7的总次数来替代。保存的内容要求为：
1）期号：即起始期号（1列）
2）第3组复式红蓝球号码（红球号码与蓝球号码各1列，号码之间用逗号隔开，共2列）
3）第3组复式红蓝球号码中红蓝球号码的个数（红球号码的个数与蓝球号码的个数各1列，共2列）
4）最大命中情况（1列）
5）最大命中情况出现的次数（1列）
6）中奖期号（1列）
7）中奖期号对应的红蓝球号码（每个号码各1列，共7列）
8）中奖期号对应的红蓝球号码的马尔科夫概率位置序号（每个位置序号各1列，共7列）。

3.4 批量测试
询问用户想选择哪一组复式红蓝球号码进行最佳数据范围的批量测试：1）第1组复式红蓝球号码的数据范围、2）第2组复式红蓝球号码的数据范围、3）第3组复式红蓝球号码的数据范围。然后根据用户选择，进入对应子功能。
3.4.1 对第1组复式红蓝球号码的数据范围进行批量测试
3.4.1.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
3）定义当前数据库的数据范围区间：询问用户，根据用户输入来定义批量测试的当前数据库的数据范围区间。用户输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给DBS_min，第2个数字传递给DBS_max。比如：用户输入5,10，则DBS_min等于5，DBS_max等于10。
3.4.1.2 循环主体
1）定义当前数据库：根据用户输入的当前数据库的数据范围区间，先将DBS_min值传递给DBS，以DBS作为当前数据库所需的数据范围，然后将原始数据库中，起始期号之前的DBS期数据（不包括起始期号对应的红蓝球号码），总共DBS期的红蓝球号码作为当前数据库。
2）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第1组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。
3）小循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第1组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况。 重复“小循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
4）大循环测试：当上一步“小循环测试”结束后，将DBS的值加1，以作为新的DBS的值。然后将当前起始期号还原成用户定义的起始期号（用户一开始输入的起始期号）。然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。然后基于当前数据库，按照“筛选号码”算法中第1组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。然后重复“小循环测试”这一步直到原始数据库不足，测试到合适的期号为止。然后再重复“大循环测试”这一步，直到DBS的值大于DBS_max，“批量测试”结束。
3.4.1.3 信息打印
程序在进行“批量测试”时，需要在一开始打印显示：需要批量测试的总大循环数（根据用户指定的当前数据库的数据范围区间计算出需要批量测试的总大循环数，即等于DBS_max的值减去DBS_min的值再加1），然后在每次大循环测试中，用百分比来显示进度，并每完成1次大循环测试之后，打印显示当前大循环测试中最大命中情况等于7的总次数。
3.4.1.4 保存结果
当完成批量测试后，需要将本次批量测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中，Excel文件的命名规则为：批量测试1_AAAA_BBBB_CCCC_DDDD_EEEE，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用DBC_min值来替代、DDDD用DBC_max值来替代、EEEE用当前答案数据库的数据范围值来替代。保存的内容要求为：
1）在Excel文件的“总体情况”标签页下保存：每1个当前数据库的数据范围值（1列），及对应的单次大循环测试中最大命中情况等于7的总次数（1列）与最大命中情况等于6的总次数（1列）。
3.4.2 对第2组复式红蓝球号码的数据范围进行批量测试
3.4.2.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义红蓝球号码的个数要求：询问用户，根据用户输入来定义第2组复式红蓝球号码所需的红蓝球号码的个数。用户需输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给NR，第2个数字传递给NB。比如：用户输入22,6，则NR等于22，NB等于6。
3）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
4）定义当前数据库的数据范围区间：询问用户，根据用户输入来定义批量测试的当前数据库的数据范围区间。用户输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给DBS_min，第2个数字传递给DBS_max。比如：用户输入15,100，则DBS_min等于15，DBS_max等于100。
3.4.2.2 循环主体
1）定义当前数据库：根据用户输入的当前数据库的数据范围区间，先将DBS_min值传递给DBS，以DBS作为当前数据库所需的数据范围，然后将原始数据库中，起始期号之前的DBS期数据（不包括起始期号对应的红蓝球号码），总共DBS期的红蓝球号码作为当前数据库。
2）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第2组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。
3）小循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第2组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况。 重复“小循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
4）大循环测试：当上一步“小循环测试”结束后，将DBS的值加1，以作为新的DBS的值。然后将当前起始期号还原成用户定义的起始期号（用户一开始输入的起始期号）。然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。然后基于当前数据库，按照“筛选号码”算法中第2组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。然后重复“小循环测试”这一步直到原始数据库不足，测试到合适的期号为止。然后再重复“大循环测试”这一步，直到DBS的值大于DBS_max，“批量测试”结束。
3.4.2.3 信息打印
程序在进行“批量测试”时，需要在一开始打印显示：需要批量测试的总大循环数（根据用户指定的当前数据库的数据范围区间计算出需要批量测试的总大循环数，即等于DBS_max的值减去DBS_min的值再加1），然后在每次大循环测试中，用百分比来显示进度，并每完成1次大循环测试之后，打印显示当前大循环测试中最大命中情况等于7的总次数。
3.4.2.4 保存结果
当完成批量测试后，需要将本次批量测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中，Excel文件的命名规则为：批量测试2_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用DBC_min值来替代、DDDD用DBC_max值来替代、EEEE用NR值来替代、FFFF用NB值来替代、GGGG用当前答案数据库的数据范围值来替代。保存的内容要求为：
1）在Excel文件的“总体情况”标签页下保存：每1个当前数据库的数据范围值（1列），及对应的单次大循环测试中最大命中情况等于7的总次数（1列）与最大命中情况等于6的总次数（1列）。
3.4.3 对第3组复式红蓝球号码的数据范围进行批量测试
3.4.3.1 输入条件
1）定义起始期号：询问用户，想从哪一期号码开始。把用户输入的期号（需匹配原始数据库中的期号格式）作为起始期号。
2）定义红蓝球号码的个数要求：询问用户，根据用户输入来定义第3组复式红蓝球号码所需的红蓝球号码的个数。用户需输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给NR，第2个数字传递给NB。比如：用户输入22,6，则NR等于22，NB等于6。
3）定义答案数据库的数据范围：询问用户，根据用户输入来定义答案数据库的数据范围。如果用户输入一个非零正整数，比如9，则将原始数据库中，起始期号及之后的8期数据，总共9期的红蓝球号码作为当前答案数据库。
4）定义当前数据库的数据范围区间：询问用户，根据用户输入来定义批量测试的当前数据库的数据范围区间。用户输入两个非零正整数，中间用逗号隔开。然后将其中第1个数字传递给DBS_min，第2个数字传递给DBS_max。比如：用户输入15,100，则DBS_min等于15，DBS_max等于100。
3.4.3.2 循环主体
1）定义当前数据库：根据用户输入的当前数据库的数据范围区间，先将DBS_min值传递给DBS，以DBS作为当前数据库所需的数据范围，然后将原始数据库中，起始期号之前的DBS期数据（不包括起始期号对应的红蓝球号码），总共DBS期的红蓝球号码作为当前数据库。
2）筛选号码并比对：基于当前数据库，按照“筛选号码”算法中第3组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。
3）小循环测试：当完成上一步“筛选号码并比对”运算后，按照原始数据库中的期号先后顺序，将起始期号往下顺延至后1期（需注意期号年度跨越的处理），以作为新的起始期号，然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。当完成更新起始期号与相关数据库之后，再运行“筛选号码并比对”这一步，即基于更新后的当前数据库，按照“筛选号码”算法中第3组复式号码的筛选方法，重新生成1组新的复式红蓝球号码，再将其与更新后的当前答案数据库按照“比对答案”算法进行比对，以统计更新后的这一组复式红蓝球号码的最大命中情况。 重复“小循环测试”这一步操作，如果原始数据库不足，则测试到合适的期号为止。
4）大循环测试：当上一步“小循环测试”结束后，将DBS的值加1，以作为新的DBS的值。然后将当前起始期号还原成用户定义的起始期号（用户一开始输入的起始期号）。然后再基于更新后的起始期号、当前数据库所需的数据范围DBS和用户定义的答案数据库的数据范围（答案数据库的数据范围值不需要用户重复输入），来更新当前数据库与当前答案数据库。然后基于当前数据库，按照“筛选号码”算法中第3组复式号码的筛选方法，生成1组复式红蓝球号码，将其与当前答案数据库按照“比对答案”算法进行比对，以统计这1组复式红蓝球号码的最大命中情况。然后重复“小循环测试”这一步直到原始数据库不足，测试到合适的期号为止。然后再重复“大循环测试”这一步，直到DBS的值大于DBS_max，“批量测试”结束。
3.4.3.3 信息打印
程序在进行“批量测试”时，需要在一开始打印显示：需要批量测试的总大循环数（根据用户指定的当前数据库的数据范围区间计算出需要批量测试的总大循环数，即等于DBS_max的值减去DBS_min的值再加1），然后在每次大循环测试中，用百分比来显示进度，并每完成1次大循环测试之后，打印显示当前大循环测试中最大命中情况等于7的总次数。
3.4.3.4 保存结果
当完成批量测试后，需要将本次批量测试的相关结果，按照下述要求，保存在“D:\Lottery”路径下的Output文件夹中的Excel文件中，Excel文件的命名规则为：批量测试3_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG，其中AAAA用数据源，即SSQ或DLT来替代、BBBB用实际日期来替代、CCCC用DBC_min值来替代、DDDD用DBC_max值来替代、EEEE用NR值来替代、FFFF用NB值来替代、GGGG用当前答案数据库的数据范围值来替代。保存的内容要求为：
1）在Excel文件的“总体情况”标签页下保存：每1个当前数据库的数据范围值（1列），及对应的单次大循环测试中最大命中情况等于7的总次数（1列）与最大命中情况等于6的总次数（1列）。

四、程序要求
4.1 模块化：相关代码片段请按模块化的思路（即不同的功能，代码模块放在不同的python文件中）来构建，便于后续修订与维护。并在保证实现本文中的所有需求的前提下，优先使用更高效的代码实现，以提高程序整体的运行效率。
4.2 注释：程序中所有代码都需要有注释说明。 
4.3 读取数据：读取的数据中，自动清空无效数据或空数据行。 
4.4 禁止程序自动对根目录下的所有的Excel文件进行删除操作，禁止程序自动对“D:\Lottery”路径下的“lottery_data_all.xlsx”进行改动。